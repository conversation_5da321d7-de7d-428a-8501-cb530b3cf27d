/* tslint:disable */
/**
 * @license
 * SPDX-License-Identifier: Apache-2.0
 */

import {GoogleGenAI, LiveServerMessage, Modality, Session} from '@google/genai';
import {LitElement, PropertyValueMap, css, html} from 'lit';
import {customElement, state} from 'lit/decorators.js';
import {createBlob, decode, decodeAudioData} from './utils';
import './visual-3d.ts';

@customElement('gdm-live-audio')
export class GdmLiveAudio extends LitElement {
  @state() isRecording = false;
  @state() status = '';
  @state() error = '';
  @state() selectedVoice = 'Orus';
  @state() transcriptionHistory: {speaker: string; text: string}[] = [];
  @state() currentInputTranscription = '';
  @state() currentOutputTranscription = '';

  private client: GoogleGenAI;
  private sessionPromise: Promise<Session>;
  // FIX: Cast window to `any` to access vendor-prefixed `webkitAudioContext` without TypeScript errors.
  private inputAudioContext = new (window.AudioContext ||
    (window as any).webkitAudioContext)({sampleRate: 16000});
  // FIX: Cast window to `any` to access vendor-prefixed `webkitAudioContext` without TypeScript errors.
  private outputAudioContext = new (window.AudioContext ||
    (window as any).webkitAudioContext)({sampleRate: 24000});
  @state() inputNode = this.inputAudioContext.createGain();
  @state() outputNode = this.outputAudioContext.createGain();
  private nextStartTime = 0;
  private mediaStream: MediaStream;
  private sourceNode: AudioBufferSourceNode;
  private scriptProcessorNode: ScriptProcessorNode;
  private sources = new Set<AudioBufferSourceNode>();
  private voices = ['Orus', 'Zephyr', 'Puck', 'Charon', 'Kore', 'Fenrir'];

  @state() private recordedBlob: Blob | null = null;
  private audioChunks: Blob[] = [];
  private mediaRecorder: MediaRecorder;
  private recorderDestinationNode: MediaStreamAudioDestinationNode;
  private micSourceForRecording: MediaStreamAudioSourceNode;

  static styles = css`
    :host {
      display: block;
      width: 100%;
      height: 100vh;
      overflow: hidden;
      position: relative;
      font-family: 'Roboto', sans-serif;

      --brand-color: #022f63;
      --brand-color-rgb: 2, 47, 99;
      --text-color-primary: #e0e6f0;
      --text-color-secondary: #a8b2c2;
      --accent-color-user: #38bdf8; /* sky-400 */
      --accent-color-ai: #4ade80; /* green-400 */
      --accent-color-rec: #ff4136;

      --surface-color: rgba(var(--brand-color-rgb), 0.65);
      --surface-blur: 24px;
      --surface-border: rgba(255, 255, 255, 0.1);

      --interactive-bg: rgba(255, 255, 255, 0.05);
      --interactive-bg-hover: rgba(255, 255, 255, 0.1);
      --interactive-border: rgba(255, 255, 255, 0.15);
      --interactive-border-hover: rgba(255, 255, 255, 0.3);
    }

    @keyframes pulse {
      0% {
        box-shadow: 0 0 0 0 rgba(255, 65, 54, 0.7);
      }
      70% {
        box-shadow: 0 0 0 10px rgba(255, 65, 54, 0);
      }
      100% {
        box-shadow: 0 0 0 0 rgba(255, 65, 54, 0);
      }
    }

    gdm-live-audio-visuals-3d {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 1;
    }

    #container {
      position: relative;
      z-index: 2;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
      width: 100vw;
    }

    #transcription {
      width: clamp(400px, 50vw, 600px);
      height: 85vh;
      max-height: 800px;
      background: var(--surface-color);
      color: var(--text-color-primary);
      display: flex;
      flex-direction: column;
      backdrop-filter: blur(var(--surface-blur));
      -webkit-backdrop-filter: blur(var(--surface-blur));
      border: 1px solid var(--surface-border);
      border-radius: 24px;
      z-index: 5;
      box-shadow: 0 8px 32px 0 rgba(0, 0, 0, 0.5);
      overflow: hidden;
    }

    .panel-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 10px 20px;
      border-bottom: 1px solid var(--surface-border);
      flex-shrink: 0;
    }

    .header-actions {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    #history {
      flex-grow: 1;
      overflow-y: auto;
      padding: 20px;
      display: flex;
      flex-direction: column;
      gap: 20px;
    }

    #current {
      padding: 20px;
      border-top: 1px solid var(--surface-border);
      flex-shrink: 0;
      min-height: 50px;
    }

    .panel-footer {
      padding: 15px 20px;
      border-top: 1px solid var(--surface-border);
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
    }

    .record-controls {
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .entry {
      display: flex;
      flex-direction: column;
      line-height: 1.5;
    }

    .entry .speaker {
      display: flex;
      align-items: center;
      gap: 8px;
      font-weight: 600;
      margin-bottom: 5px;
      font-size: 0.9em;
      color: var(--text-color-primary);
    }

    .entry .speaker::before {
      content: '';
      display: inline-block;
      width: 8px;
      height: 8px;
      border-radius: 50%;
    }

    .entry.human .speaker::before {
      background-color: var(--accent-color-user);
    }

    .entry.ai .speaker::before {
      background-color: var(--accent-color-ai);
    }

    .entry p {
      margin: 0;
      padding-left: 16px; /* Indent text under speaker label */
      white-space: pre-wrap;
      word-wrap: break-word;
      color: var(--text-color-primary);
      opacity: 0.9;
    }

    /* Custom scrollbar */
    #history::-webkit-scrollbar {
      width: 6px;
    }

    #history::-webkit-scrollbar-track {
      background: transparent;
    }

    #history::-webkit-scrollbar-thumb {
      background-color: rgba(255, 255, 255, 0.2);
      border-radius: 3px;
    }

    #status {
      text-align: center;
      color: var(--text-color-secondary);
      text-shadow: 0 0 4px black;
      font-size: 0.9em;
      min-height: 1.2em;
    }

    select,
    button {
      outline: none;
      border: 1px solid var(--interactive-border);
      color: var(--text-color-primary);
      background: var(--interactive-bg);
      cursor: pointer;
      padding: 0;
      margin: 0;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: background-color 0.2s, border-color 0.2s,
        transform 0.1s ease-out;
    }

    button:active {
      transform: scale(0.95);
    }

    select:hover,
    button:hover {
      background: var(--interactive-bg-hover);
      border-color: var(--interactive-border-hover);
    }

    select {
      border-radius: 8px;
      height: 36px;
      padding: 0 10px;
      font-size: 14px;
      -webkit-appearance: none;
      -moz-appearance: none;
      appearance: none;
      background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='rgba(255, 255, 255, 0.7)' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpolyline points='6 9 12 15 18 9'%3e%3c/polyline%3e%3c/svg%3e");
      background-repeat: no-repeat;
      background-position: right 10px center;
      background-size: 1em;
      padding-right: 2.5em;
    }

    select option {
      background: var(--brand-color);
      color: white;
    }

    /* Main record buttons */
    #startButton,
    #stopButton {
      width: 64px;
      height: 64px;
      border-radius: 50%;
    }

    #startButton {
      animation: pulse 2s infinite;
    }

    /* Header action buttons */
    #resetButton,
    #downloadButton {
      width: 36px;
      height: 36px;
      border-radius: 8px;
    }

    #startButton[disabled],
    #stopButton[disabled] {
      display: none;
    }

    /* Non-toggle buttons are visually disabled instead of hidden */
    #resetButton:disabled,
    #downloadButton:disabled,
    select:disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    #resetButton:disabled:hover,
    #downloadButton:disabled:hover,
    select:disabled:hover {
      background: var(--interactive-bg);
      border-color: var(--interactive-border);
    }
  `;

  constructor() {
    super();
    this.initClient();
  }

  protected updated(
    changedProperties: PropertyValueMap<any> | Map<PropertyKey, unknown>,
  ): void {
    if (
      changedProperties.has('transcriptionHistory') ||
      changedProperties.has('currentInputTranscription') ||
      changedProperties.has('currentOutputTranscription')
    ) {
      // FIX: Use `(this as HTMLElement).shadowRoot` to query the shadow DOM. The compiler was unable to find `shadowRoot` on the component's type.
      const historyEl = (this as HTMLElement).shadowRoot!.querySelector('#history');
      if (historyEl) {
        // Scroll to the bottom to show the latest message
        historyEl.scrollTop = historyEl.scrollHeight;
      }
    }
  }

  private initAudio() {
    this.nextStartTime = this.outputAudioContext.currentTime;
  }

  private async initClient() {
    this.initAudio();

    this.client = new GoogleGenAI({
      apiKey: process.env.API_KEY,
    });

    this.outputNode.connect(this.outputAudioContext.destination);

    this.initSession();
  }

  private initSession() {
    const model = 'gemini-2.5-flash-native-audio-preview-09-2025';

    this.sessionPromise = this.client.live.connect({
      model: model,
      callbacks: {
        onopen: () => {
          this.updateStatus('Opened');
        },
        onmessage: async (message: LiveServerMessage) => {
          const audio =
            message.serverContent?.modelTurn?.parts[0]?.inlineData;

          if (audio) {
            this.nextStartTime = Math.max(
              this.nextStartTime,
              this.outputAudioContext.currentTime,
            );

            const audioBuffer = await decodeAudioData(
              decode(audio.data),
              this.outputAudioContext,
              24000,
              1,
            );
            const source = this.outputAudioContext.createBufferSource();
            source.buffer = audioBuffer;
            source.connect(this.outputNode);
            source.addEventListener('ended', () => {
              this.sources.delete(source);
            });

            source.start(this.nextStartTime);
            this.nextStartTime = this.nextStartTime + audioBuffer.duration;
            this.sources.add(source);
          }

          if (message.serverContent?.inputTranscription) {
            this.currentInputTranscription +=
              message.serverContent.inputTranscription.text;
          }
          if (message.serverContent?.outputTranscription) {
            this.currentOutputTranscription +=
              message.serverContent.outputTranscription.text;
          }

          if (message.serverContent?.turnComplete) {
            let newHistory = [...this.transcriptionHistory];
            if (this.currentInputTranscription.trim()) {
              newHistory.push({
                speaker: 'human',
                text: this.currentInputTranscription.trim(),
              });
            }
            if (this.currentOutputTranscription.trim()) {
              newHistory.push({
                speaker: 'ai',
                text: this.currentOutputTranscription.trim(),
              });
            }
            this.transcriptionHistory = newHistory;
            this.currentInputTranscription = '';
            this.currentOutputTranscription = '';
          }

          const interrupted = message.serverContent?.interrupted;
          if (interrupted) {
            for (const source of this.sources.values()) {
              source.stop();
              this.sources.delete(source);
            }
            this.nextStartTime = 0;
          }
        },
        onerror: (e: ErrorEvent) => {
          this.updateError(e.message);
        },
        onclose: (e: CloseEvent) => {
          this.updateStatus('Close:' + e.reason);
        },
      },
      config: {
        responseModalities: [Modality.AUDIO],
        speechConfig: {
          voiceConfig: {prebuiltVoiceConfig: {voiceName: this.selectedVoice}},
        },
        inputAudioTranscription: {},
        outputAudioTranscription: {},
      },
    });

    this.sessionPromise.catch((e) => {
      console.error(e);
      this.updateError(e.message || 'Session connection failed');
    });
  }

  private handleVoiceChange(e: Event) {
    this.selectedVoice = (e.target as HTMLSelectElement).value;
    this.reset();
  }

  private updateStatus(msg: string) {
    this.status = msg;
    this.error = '';
  }

  private updateError(msg: string) {
    this.error = msg;
  }

  private async startRecording() {
    if (this.isRecording) {
      return;
    }

    this.recordedBlob = null;
    this.audioChunks = [];

    this.inputAudioContext.resume();
    this.outputAudioContext.resume();

    this.updateStatus('Requesting microphone access...');

    try {
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: true,
        video: false,
      });

      this.updateStatus('Microphone access granted. Starting capture...');

      this.sourceNode = this.inputAudioContext.createMediaStreamSource(
        this.mediaStream,
      );
      this.sourceNode.connect(this.inputNode);

      const bufferSize = 4096;
      this.scriptProcessorNode = this.inputAudioContext.createScriptProcessor(
        bufferSize,
        1,
        1,
      );

      this.scriptProcessorNode.onaudioprocess = (audioProcessingEvent) => {
        if (!this.isRecording) return;
        const inputBuffer = audioProcessingEvent.inputBuffer;
        const pcmData = inputBuffer.getChannelData(0);
        this.sessionPromise.then((session) => {
          session.sendRealtimeInput({media: createBlob(pcmData)});
        });
      };

      this.sourceNode.connect(this.scriptProcessorNode);
      this.scriptProcessorNode.connect(this.inputAudioContext.destination);

      // --- Audio Recording Setup ---
      // Create a destination node in the output context to capture the mixed audio.
      this.recorderDestinationNode =
        this.outputAudioContext.createMediaStreamDestination();

      // Connect the AI's audio output to the recording destination.
      this.outputNode.connect(this.recorderDestinationNode);

      // Create a source from the user's microphone stream in the output context
      // and connect it to the recording destination. This mixes the user's voice.
      this.micSourceForRecording =
        this.outputAudioContext.createMediaStreamSource(this.mediaStream);
      this.micSourceForRecording.connect(this.recorderDestinationNode);

      // Initialize MediaRecorder with the stream from the destination node,
      // which now contains the mixed audio of both the user and the AI.
      this.mediaRecorder = new MediaRecorder(
        this.recorderDestinationNode.stream,
        {
          mimeType: 'audio/webm',
        },
      );

      this.mediaRecorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          this.audioChunks.push(event.data);
        }
      };

      this.mediaRecorder.onstop = () => {
        this.recordedBlob = new Blob(this.audioChunks, {type: 'audio/webm'});
        this.audioChunks = [];
        this.updateStatus('Recording finished. Ready to download.');
      };

      this.mediaRecorder.start();
      // --- End Audio Recording Setup ---

      this.isRecording = true;
      this.updateStatus('🔴 Recording conversation...');
    } catch (err) {
      console.error('Error starting recording:', err);
      this.updateStatus(`Error: ${err.message}`);
      this.stopRecording();
    }
  }

  private stopRecording() {
    if (!this.isRecording && !this.mediaStream && !this.inputAudioContext)
      return;

    this.updateStatus('Stopping recording...');

    if (this.mediaRecorder && this.mediaRecorder.state === 'recording') {
      this.mediaRecorder.stop();
    }

    this.isRecording = false;

    if (this.scriptProcessorNode && this.sourceNode && this.inputAudioContext) {
      this.scriptProcessorNode.disconnect();
      this.sourceNode.disconnect();
    }

    if (this.recorderDestinationNode) {
      this.outputNode.disconnect(this.recorderDestinationNode);
      if (this.micSourceForRecording) {
        this.micSourceForRecording.disconnect(this.recorderDestinationNode);
        this.micSourceForRecording = null;
      }
      this.recorderDestinationNode = null;
    }

    this.scriptProcessorNode = null;
    this.sourceNode = null;

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach((track) => track.stop());
      this.mediaStream = null;
    }
  }

  private reset() {
    this.transcriptionHistory = [];
    this.currentInputTranscription = '';
    this.currentOutputTranscription = '';
    this.recordedBlob = null;
    this.audioChunks = [];

    if (this.sessionPromise) {
      this.sessionPromise
        .then((session) => session.close())
        .catch((err) => {
          console.debug('Error closing previous session:', err);
        });
    }
    this.initSession();
    this.updateStatus('Session cleared.');
  }

  private downloadRecording() {
    if (!this.recordedBlob) {
      this.updateError('No recording available to download.');
      return;
    }

    const url = URL.createObjectURL(this.recordedBlob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = `conversation-${new Date().toISOString()}.webm`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    a.remove();
  }

  render() {
    return html`
      <gdm-live-audio-visuals-3d
        .inputNode=${this.inputNode}
        .outputNode=${this.outputNode}></gdm-live-audio-visuals-3d>
      <div id="container">
        <div id="transcription">
          <div class="panel-header">
            <select
              @change=${this.handleVoiceChange}
              .value=${this.selectedVoice}
              ?disabled=${this.isRecording}>
              ${this.voices.map(
                (voice) => html`<option value=${voice}>${voice}</option>`,
              )}
            </select>
            <div class="header-actions">
              <button
                id="resetButton"
                @click=${this.reset}
                ?disabled=${this.isRecording}
                title="Reset session">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="24px"
                  viewBox="0 -960 960 960"
                  width="24px"
                  fill="currentColor">
                  <path
                    d="M480-160q-134 0-227-93t-93-227q0-134 93-227t227-93q69 0 132 28.5T720-690v-110h80v280H520v-80h168q-32-56-87.5-88T480-720q-100 0-170 70t-70 170q0 100 70 170t170 70q77 0 139-44t87-116h84q-28 106-114 173t-196 67Z" />
                </svg>
              </button>
              <button
                id="downloadButton"
                title="Download conversation"
                @click=${this.downloadRecording}
                ?disabled=${!this.recordedBlob || this.isRecording}>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  height="24px"
                  viewBox="0 -960 960 960"
                  width="24px"
                  fill="currentColor">
                  <path
                    d="M480-320 280-520l56-58 104 104v-326h80v326l104-104 56 58-200 200ZM240-160q-33 0-56.5-23.5T160-240v-120h80v120h480v-120h80v120q0 33-23.5 56.5T720-160H240Z" />
                </svg>
              </button>
            </div>
          </div>
          <div id="history">
            ${this.transcriptionHistory.map(
              (entry) => html`
                <div class="entry ${entry.speaker}">
                  <span class="speaker"
                    >${entry.speaker === 'human' ? 'You' : 'AI'}</span
                  >
                  <p>${entry.text}</p>
                </div>
              `,
            )}
          </div>
          <div id="current">
            ${this.currentInputTranscription
              ? html`
                  <div class="entry human">
                    <span class="speaker">You</span>
                    <p>${this.currentInputTranscription}</p>
                  </div>
                `
              : ''}
            ${this.currentOutputTranscription
              ? html`
                  <div class="entry ai">
                    <span class="speaker">AI</span>
                    <p>${this.currentOutputTranscription}</p>
                  </div>
                `
              : ''}
          </div>
          <div class="panel-footer">
            <div class="record-controls">
              <button
                id="startButton"
                @click=${this.startRecording}
                ?disabled=${this.isRecording}
                title="Start recording">
                <svg
                  viewBox="0 0 100 100"
                  width="32px"
                  height="32px"
                  fill="var(--accent-color-rec)"
                  xmlns="http://www.w3.org/2000/svg">
                  <circle cx="50" cy="50" r="50" />
                </svg>
              </button>
              <button
                id="stopButton"
                @click=${this.stopRecording}
                ?disabled=${!this.isRecording}
                title="Stop recording">
                <svg
                  viewBox="0 0 100 100"
                  width="32px"
                  height="32px"
                  fill="currentColor"
                  xmlns="http://www.w3.org/2000/svg">
                  <rect x="15" y="15" width="70" height="70" rx="10" />
                </svg>
              </button>
            </div>
            <div id="status"> ${this.error || this.status} </div>
          </div>
        </div>
      </div>
    `;
  }
}